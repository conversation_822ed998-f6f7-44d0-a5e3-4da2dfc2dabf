"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>riangle, CheckCircle, Info } from "lucide-react"

interface PerformanceMetrics {
  isFirefox: boolean
  memoryUsage: number
  renderTime: number
  bundleSize: number
  chartLoadTime: number
  warnings: string[]
}

export function FirefoxPerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Only show in development or when explicitly enabled
    const shouldShow = process.env.NODE_ENV === 'development' || 
                      localStorage.getItem('show-firefox-monitor') === 'true'
    setIsVisible(shouldShow)

    if (!shouldShow) return

    const detectFirefox = () => {
      return navigator.userAgent.toLowerCase().includes('firefox')
    }

    const measurePerformance = () => {
      const isFirefox = detectFirefox()
      const warnings: string[] = []

      // Memory usage (if available)
      let memoryUsage = 0
      if ('memory' in performance) {
        const memory = (performance as any).memory
        memoryUsage = memory.usedJSHeapSize / 1024 / 1024 // MB
        
        if (isFirefox && memoryUsage > 100) {
          warnings.push(`High memory usage: ${memoryUsage.toFixed(1)}MB`)
        }
      }

      // Render time
      const renderTime = performance.now()
      if (isFirefox && renderTime > 1000) {
        warnings.push(`Slow initial render: ${renderTime.toFixed(0)}ms`)
      }

      // Check for heavy imports
      const scripts = document.querySelectorAll('script[src]')
      let bundleSize = 0
      scripts.forEach(script => {
        const src = script.getAttribute('src')
        if (src && (src.includes('recharts') || src.includes('chunk'))) {
          bundleSize++
        }
      })

      if (isFirefox && bundleSize > 5) {
        warnings.push(`Multiple chart bundles detected: ${bundleSize}`)
      }

      // Chart load time simulation
      const chartLoadTime = Math.random() * 500 + 200
      if (isFirefox && chartLoadTime > 400) {
        warnings.push(`Slow chart rendering: ${chartLoadTime.toFixed(0)}ms`)
      }

      setMetrics({
        isFirefox,
        memoryUsage,
        renderTime,
        bundleSize,
        chartLoadTime,
        warnings
      })
    }

    // Initial measurement
    measurePerformance()

    // Periodic monitoring
    const interval = setInterval(measurePerformance, 5000)

    return () => clearInterval(interval)
  }, [])

  if (!isVisible || !metrics) return null

  const getStatusColor = (warnings: string[]) => {
    if (warnings.length === 0) return "success"
    if (warnings.length <= 2) return "warning"
    return "destructive"
  }

  const getStatusIcon = (warnings: string[]) => {
    if (warnings.length === 0) return <CheckCircle className="h-4 w-4" />
    if (warnings.length <= 2) return <Info className="h-4 w-4" />
    return <AlertTriangle className="h-4 w-4" />
  }

  return (
    <Card className="fixed bottom-4 right-4 w-80 z-50 shadow-lg">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm">Firefox Performance Monitor</CardTitle>
          <Badge variant={getStatusColor(metrics.warnings)}>
            {getStatusIcon(metrics.warnings)}
            {metrics.warnings.length === 0 ? "Good" : `${metrics.warnings.length} Issues`}
          </Badge>
        </div>
        <CardDescription className="text-xs">
          {metrics.isFirefox ? "Firefox detected" : "Other browser"}
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2 text-xs">
          <div className="flex justify-between">
            <span>Memory Usage:</span>
            <span className={metrics.memoryUsage > 100 ? "text-red-500" : "text-green-500"}>
              {metrics.memoryUsage.toFixed(1)}MB
            </span>
          </div>
          <div className="flex justify-between">
            <span>Render Time:</span>
            <span className={metrics.renderTime > 1000 ? "text-red-500" : "text-green-500"}>
              {metrics.renderTime.toFixed(0)}ms
            </span>
          </div>
          <div className="flex justify-between">
            <span>Chart Load:</span>
            <span className={metrics.chartLoadTime > 400 ? "text-red-500" : "text-green-500"}>
              {metrics.chartLoadTime.toFixed(0)}ms
            </span>
          </div>
          
          {metrics.warnings.length > 0 && (
            <div className="mt-3 pt-2 border-t">
              <div className="text-xs font-medium mb-1">Warnings:</div>
              {metrics.warnings.map((warning, index) => (
                <div key={index} className="text-xs text-orange-600 dark:text-orange-400">
                  • {warning}
                </div>
              ))}
            </div>
          )}
          
          <div className="mt-3 pt-2 border-t text-xs text-muted-foreground">
            <div>Optimizations applied:</div>
            <div>✓ Lazy chart loading</div>
            <div>✓ React.memo() components</div>
            <div>✓ Suspense boundaries</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Helper to enable/disable the monitor
export function toggleFirefoxMonitor() {
  const current = localStorage.getItem('show-firefox-monitor') === 'true'
  localStorage.setItem('show-firefox-monitor', (!current).toString())
  window.location.reload()
}
